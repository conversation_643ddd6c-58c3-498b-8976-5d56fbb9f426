package com.intuit.appintgwkflw.wkflautomate.was.app.providers;

import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.DefinitionServiceImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.rbac.DefinitionRbacService;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.DefinitionDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.v4.Authorization;
import com.intuit.v4.workflows.Definition;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ReadAllDefinitionTest {
  private static final String REALM_ID = "12345";
  private final Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  @Rule public ExpectedException exceptionRule = ExpectedException.none();
  @Mock private DefinitionDetailsRepository definitionDetailsRepository;
  @Mock private DefinitionServiceHelper definitionServiceHelper;
  @InjectMocks private DefinitionServiceImpl definitionService;
  @Mock private DefinitionRbacService definitionRbacService;
  @Mock private TemplateDetails bpmnTemplateDetail;
  @Mock private LookupKeysMapper lookupKeysMapper;
  private List<DefinitionDetails> definitionDetailsList;
  private List<DefinitionDetails> emptyDefinitionDetailsList;

  @Before
  public void setUp() {
    definitionDetailsList = new ArrayList<>();
    emptyDefinitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(
        TestHelper.mockDefinitionDetailsWithInternalStatus(
            bpmnTemplateDetail, authorization, InternalStatus.MARKED_FOR_DISABLE));
    definitionDetailsList.add(
        TestHelper.mockDefinitionDetailsWithInternalStatus(
            bpmnTemplateDetail, authorization, null));
  }

  @Test
  public void readAllDefinitionSuccess() {
    // Mock RBAC service to allow access
    Mockito.doNothing().when(definitionRbacService).verifyDefinitionAccess(Mockito.any(), Mockito.any(), Mockito.any());

    // Mock the helper method that the service actually calls
    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(Long.parseLong(REALM_ID), null))
        .thenReturn(Optional.of(definitionDetailsList));

    List<Definition> result = definitionService.getDefinitionList(authorization, null);
    Assert.assertNotNull(result);
    Assert.assertFalse(result.isEmpty());
  }

  @Test
  public void testNoDefinitionFailure() {
    // Mock RBAC service to allow access
    Mockito.doNothing().when(definitionRbacService).verifyDefinitionAccess(Mockito.any(), Mockito.any(), Mockito.any());

    // Mock the helper method to return empty list
    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(Long.parseLong(REALM_ID), null))
        .thenReturn(Optional.of(emptyDefinitionDetailsList));

    List<Definition> result = definitionService.getDefinitionList(authorization, null);
    Assert.assertNotNull(result);
    Assert.assertEquals(0, result.size());
  }
}
